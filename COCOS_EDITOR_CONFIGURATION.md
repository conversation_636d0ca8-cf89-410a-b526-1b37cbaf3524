# Cocos Creator 编辑器配置指南

## 🎯 重要提醒
**我们的代码修改不需要在编辑器中进行额外配置**，但请确保以下现有配置正确无误。

## 📋 必须检查的配置项

### 1. Game.scene 场景配置

#### GameManager 组件配置
在 `Game.scene` 中找到 GameManager 节点，检查以下属性：

**必须配置的属性：**
- ✅ **coinSpawner**: 必须拖拽 CoinSpawner 组件节点
- ✅ **pipeSpawner**: 必须拖拽 PipeSpawner 组件节点（或通过 backgroundManager 管理）
- ✅ **backgroundManager**: 必须拖拽 BackgroundManager 组件节点
- ✅ **windChallengeManager**: 必须拖拽 WindChallengeManager 组件节点

#### CoinSpawner 组件配置
找到 CoinSpawner 节点，检查以下属性：

**关键配置项：**
- ✅ **coinPrefab**: 必须拖拽金币预制体 (`assets/prefabs/Coin.prefab`)
- ✅ **spawnRate**: 基础生成间隔 (默认: 3)
- ✅ **minHeight**: 最小高度 (默认: 520)
- ✅ **maxHeight**: 最大高度 (默认: 920)
- ✅ **xOffset**: X轴偏移量 (默认: -165)
- ✅ **windModeXOffset**: 大风吹模式额外偏移 (默认: 80)

#### PipeSpawner 组件配置
找到 PipeSpawner 节点，检查以下属性：

**关键配置项：**
- ✅ **pipePrefab**: 必须拖拽管道预制体
- ✅ **spawnRate**: 基础生成间隔 (默认: 3)

### 2. 预制体配置

#### Coin.prefab 配置
确保金币预制体包含：
- ✅ **Coin 组件**: 必须添加 Coin 脚本组件
- ✅ **碰撞器**: 用于检测收集
- ✅ **动画组件**: 金币旋转动画

#### 管道预制体配置
确保管道预制体包含：
- ✅ **Pipe 组件**: 必须添加 Pipe 脚本组件
- ✅ **碰撞器**: 用于检测碰撞

## 🔧 配置步骤

### 步骤1: 打开 Game.scene
1. 在 Cocos Creator 中打开 `assets/scenes/Game.scene`
2. 在层级管理器中找到相关节点

### 步骤2: 检查 GameManager 配置
1. 选择 GameManager 节点
2. 在属性检查器中找到 GameManager 组件
3. 确保以下属性已正确拖拽：
   - `coinSpawner` → CoinSpawner 节点
   - `backgroundManager` → BackgroundManager 节点
   - `windChallengeManager` → WindChallengeManager 节点

### 步骤3: 检查 CoinSpawner 配置
1. 选择 CoinSpawner 节点
2. 在属性检查器中找到 CoinSpawner 组件
3. 确保 `coinPrefab` 已拖拽 `assets/prefabs/Coin.prefab`
4. 检查数值配置是否合理

### 步骤4: 检查 PipeSpawner 配置
1. 选择 PipeSpawner 节点（可能在 BackgroundManager 下）
2. 确保 `pipePrefab` 已正确配置

## ⚠️ 常见问题排查

### 问题1: "coinSpawner未设置" 错误
**解决方案：**
1. 检查 GameManager 的 `coinSpawner` 属性是否为空
2. 将 CoinSpawner 节点拖拽到该属性

### 问题2: "金币预制体实例化失败" 错误
**解决方案：**
1. 检查 CoinSpawner 的 `coinPrefab` 属性
2. 确保拖拽的是正确的 Coin.prefab 文件

### 问题3: 金币没有 Coin 组件错误
**解决方案：**
1. 打开 `assets/prefabs/Coin.prefab`
2. 确保根节点添加了 Coin 脚本组件

## 🎮 测试配置

### 配置验证清单
- [ ] GameManager.coinSpawner 已配置
- [ ] CoinSpawner.coinPrefab 已配置
- [ ] Coin.prefab 包含 Coin 组件
- [ ] 控制台无配置错误信息

### 运行时检查
启动游戏后，在控制台应该看到：
```
CoinSpawner初始化完成
CoinSpawner配置: spawnRate= 3 minHeight= 520 maxHeight= 920 xOffset= -165 windModeXOffset= 80
CoinSpawner预制体: 已设置
```

## 📝 注意事项

1. **不需要修改现有配置值** - 我们的距离控制是代码层面的改进
2. **保持原有的预制体和组件配置** - 只是改变了生成逻辑
3. **确保所有引用都正确** - 特别是预制体引用
4. **测试前先保存场景** - 避免配置丢失

## 🚀 完成后的测试
配置完成后，运行游戏并测试复活功能，金币应该能正确生成在管道中间位置。
