import { _decorator, Component, Node, Prefab, instantiate, Vec3, math } from 'cc';
import { Coin } from './Coin';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { WindChallengeManager } from './WindChallengeManager';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

@ccclass('CoinSpawner')
export class CoinSpawner extends Component {
    @property(Prefab)
    coinPrefab: Prefab = null; // 金币预制体

    @property
    spawnRate: number = 3; // 基础生成间隔（轻松难度）

    // 不同难度的生成间隔倍率
    private readonly SPAWN_RATE_MULTIPLIER_EASY: number = 1.0;
    private readonly SPAWN_RATE_MULTIPLIER_NORMAL: number = 0.833; // 1/1.2
    private readonly SPAWN_RATE_MULTIPLIER_HARD: number = 0.667;   // 1/1.5

    private _currentSpawnRate: number = 3; // 当前实际使用的生成间隔

    @property
    minHeight: number = 520; // 最小高度，确保在地板上方

    @property
    maxHeight: number = 920; // 最大高度

    @property
    xOffset: number = -165; // 金币生成的X轴偏移量，可以在编辑器中调整

    @property
    windModeXOffset: number = 80; // 大风吹模式下的额外X轴偏移量

    private timer: number = 2; // 初始计时器值，与PipeSpawner保持一致
    private _isSpawning: boolean = false;
    private _isWindMode: boolean = false; // 是否为大风吹模式
    private _isDistanceMode: boolean = false; // 是否为距离控制模式

    // 距离控制参数
    private readonly TOTAL_PIPE_DISTANCE: number = 600;    // 相邻管道的总距离
    private _totalDistanceTraveled: number = 0;  // 总移动距离
    private _nextCoinDistance: number = 600;     // 下次生成金币的距离（第一个金币在600距离处）

    start() {
        // 初始化
        console.log("CoinSpawner初始化完成");
        console.log("CoinSpawner配置: spawnRate=", this.spawnRate, "minHeight=", this.minHeight, "maxHeight=", this.maxHeight, "xOffset=", this.xOffset, "windModeXOffset=", this.windModeXOffset);
        console.log("CoinSpawner预制体: ", this.coinPrefab ? "已设置" : "未设置");
    }

    update(deltaTime: number) {
        // 大风吹模式下不使用定时器生成
        if(this._isWindMode) return;

        // 距离控制模式下处理距离更新
        if (this._isDistanceMode) {
            this.updateDistanceMode(deltaTime);
            return;
        }

        if (!this._isSpawning) {
            // 如果不是第一次检查，不要重复输出
            if (this.timer !== -999) {
                console.log("CoinSpawner未启动");
                this.timer = -999; // 特殊值，表示已经输出过日志
            }
            return;
        }

        // 确保deltaTime是合理的值
        if (deltaTime > 1) {
            console.warn("CoinSpawner警告: deltaTime异常大: ", deltaTime);
            deltaTime = 0.016; // 使用一个合理的默认值
        }

        // 累加计时器
        this.timer += deltaTime;

        // 输出调试信息（每秒输出一次）
        if (Math.floor(this.timer) !== Math.floor(this.timer - deltaTime)) {
            console.log("CoinSpawner计时器: ", this.timer.toFixed(2), "/", this.spawnRate);
        }

        // 当计时器超过生成间隔时，生成一个金币
        if (this.timer >= this._currentSpawnRate) {
            console.log("CoinSpawner触发生成金币，计时器值: ", this.timer.toFixed(2));
            this.timer = 0; // 重置计时器
            this.spawnCoin();
        }
    }

    spawnCoin() {
        if (!this.coinPrefab) {
            console.error("CoinSpawner错误: coinPrefab未设置!");
            return;
        }

        try {
            // 获取CoinSpawner的世界坐标
            const spawnerPos = this.node.getWorldPosition();
            console.log("CoinSpawner位置: ", spawnerPos);

            // 随机高度，确保使用完整的高度范围
            const randomHeight = math.randomRange(this.minHeight, this.maxHeight);
            console.log("生成金币随机高度: ", randomHeight, "范围: ", this.minHeight, "到", this.maxHeight);

            // 实例化金币
            console.log("开始实例化金币预制体");
            const coin = instantiate(this.coinPrefab);
            if (!coin) {
                console.error("CoinSpawner错误: 金币预制体实例化失败!");
                return;
            }

            console.log("金币预制体实例化成功，添加到节点");
            this.node.addChild(coin);

            // 设置金币位置，X坐标加上偏移量，使其出现在屏幕右侧
            let finalX = spawnerPos.x + this.xOffset;

            // 在大风吹模式下，添加额外的偏移量
            if (this._isWindMode) {
                finalX += this.windModeXOffset;
                console.log(`大风吹模式：金币位置向右偏移 ${this.windModeXOffset}`);
            }

            console.log("设置金币位置: ", finalX, randomHeight);
            coin.setWorldPosition(new Vec3(finalX, randomHeight, 0));

            // 确保金币是激活的
            coin.active = true;

            // 检查金币组件
            const coinComponent = coin.getComponent(Coin);
            if (coinComponent) {
                console.log("金币组件已找到");
                // 在大风吹模式下，设置金币的动态速度
                if (this._isWindMode) {
                    (coinComponent as any).setWindMode(true);
                }
            } else {
                console.error("CoinSpawner错误: 金币预制体上没有Coin组件!");
            }

            console.log(`生成金币成功! 位置: x=${finalX}, y=${randomHeight}`);
        } catch (error) {
            console.error("CoinSpawner错误: ", error);
        }
    }

    public pause() {
        this._isSpawning = false;

        // 禁用所有金币的移动
        const coins = this.node.children;
        for (let i = 0; i < coins.length; i++) {
            const coin = coins[i].getComponent(Coin);
            if (coin) {
                coin.enabled = false;
            }
        }
    }

    public startSpawning() {
        // 防止重复启动
        if (this._isSpawning) {
            console.log("金币生成器已经在运行中，忽略重复启动");
            return;
        }

        // 根据当前难度设置生成间隔
        this.updateSpawnRateByDifficulty();

        this._isSpawning = true;
        // 根据难度设置不同的初始计时器值
        const currentDifficulty = GameDifficulty.getDifficulty();

        if (currentDifficulty === GameDifficulty.DIFFICULTY_NORMAL) {
            // 标准难度：初始计时器值为0.35
            this.timer = 0.35;
        } else if (currentDifficulty === GameDifficulty.DIFFICULTY_HARD) {
            // 困难难度：初始计时器值为0.7
            this.timer = 0.7;
        } else {
            // 轻松难度：初始计时器值为0
            this.timer = 0;
        }

        console.log(`金币生成器初始计时器值设置为: ${this.timer.toFixed(2)} (难度: ${currentDifficulty})`);
        console.log("金币生成器已启动，生成间隔: " + this._currentSpawnRate.toFixed(2) + "秒");
    }

    // 根据当前难度更新生成间隔
    private updateSpawnRateByDifficulty() {
        const currentDifficulty = GameDifficulty.getDifficulty();

        // 根据难度设置生成间隔倍率
        let spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_EASY;

        switch(currentDifficulty) {
            case GameDifficulty.DIFFICULTY_NORMAL:
                spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_NORMAL;
                break;
            case GameDifficulty.DIFFICULTY_HARD:
                spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_HARD;
                break;
        }

        // 计算实际生成间隔
        this._currentSpawnRate = this.spawnRate * spawnRateMultiplier;

        console.log(`金币生成间隔设置为: ${this._currentSpawnRate.toFixed(2)}秒 (难度: ${currentDifficulty})`);
    }

    /**
     * 启动大风吹模式
     */
    public startWindMode(): void {
        this._isWindMode = true;
        this._isSpawning = true;
        console.log("金币生成器：启动大风吹模式（距离控制）");
    }

    /**
     * 强制生成一个金币（用于大风吹模式的距离控制）
     */
    public forceSpawnCoin(): void {
        if ((this._isWindMode || this._isDistanceMode) && this._isSpawning) {
            this.spawnCoin();
            console.log("金币生成器：强制生成金币（距离控制模式）");
        }
    }

    /**
     * 启动距离控制模式
     */
    public startDistanceMode(): void {
        this._isDistanceMode = true;
        this._isSpawning = true;
        this.resetDistanceTracking();
        console.log("金币生成器：启动距离控制模式");
    }

    /**
     * 更新距离控制模式
     */
    private updateDistanceMode(deltaTime: number): void {
        if (!this._isSpawning) return;

        // 获取当前移动速度
        const currentSpeed = GameManager.inst().getCurrentMoveSpeed();

        // 累加移动距离
        this._totalDistanceTraveled += currentSpeed * deltaTime;

        // 检查是否需要生成金币
        this.checkCoinSpawn();
    }

    /**
     * 检查是否需要生成金币
     */
    private checkCoinSpawn(): void {
        if (this._totalDistanceTraveled >= this._nextCoinDistance) {
            // 生成金币
            this.spawnCoin();

            // 更新距离记录
            this._nextCoinDistance = this._totalDistanceTraveled + this.TOTAL_PIPE_DISTANCE;

            console.log(`距离控制生成金币 - 距离: ${this._totalDistanceTraveled.toFixed(2)}, 下次金币距离: ${this._nextCoinDistance.toFixed(2)}`);
        }
    }

    /**
     * 重置距离追踪
     */
    public resetDistanceTracking(): void {
        this._totalDistanceTraveled = 0;
        this._nextCoinDistance = 600;  // 第一个金币在600距离处（第一个管道后300距离）
        console.log("金币生成器：距离追踪已重置");
    }

    /**
     * 获取是否为距离控制模式
     */
    public isDistanceMode(): boolean {
        return this._isDistanceMode;
    }
}


