# 距离控制生成系统实现文档

## 问题描述
复活后金币生成的位置不在相邻管道的中间线上了，只有挑战模式中的大风吹关卡还可以正常生成在相邻管道中间线上，因为这个关卡是基于距离控制生成的，而其它关卡的生成方式是基于时间控制生成的。

## 解决方案
将所有非大风吹模式的关卡从基于时间控制生成改为基于距离控制生成，参考大风吹关卡的实现。

## 修改内容

### 1. CoinSpawner.ts 修改
- 添加了距离控制模式支持
- 新增属性：
  - `_isDistanceMode`: 是否为距离控制模式
  - `TOTAL_PIPE_DISTANCE`: 相邻管道的总距离 (600)
  - `_totalDistanceTraveled`: 总移动距离
  - `_nextCoinDistance`: 下次生成金币的距离

- 新增方法：
  - `startDistanceMode()`: 启动距离控制模式
  - `updateDistanceMode()`: 更新距离控制模式
  - `checkCoinSpawn()`: 检查是否需要生成金币
  - `resetDistanceTracking()`: 重置距离追踪
  - `isDistanceMode()`: 获取是否为距离控制模式

### 2. PipeSpawner.ts 修改
- 添加了距离控制模式支持
- 新增属性：
  - `_isDistanceMode`: 是否为距离控制模式
  - `TOTAL_PIPE_DISTANCE`: 相邻管道的总距离 (600)
  - `_totalDistanceTraveled`: 总移动距离
  - `_nextPipeDistance`: 下次生成管道的距离

- 新增方法：
  - `startDistanceMode()`: 启动距离控制模式
  - `updateDistanceMode()`: 更新距离控制模式
  - `checkPipeSpawn()`: 检查是否需要生成管道
  - `resetDistanceTracking()`: 重置距离追踪
  - `isDistanceMode()`: 获取是否为距离控制模式

### 3. GameManager.ts 修改
- 在 `transitionToGamingState()` 方法中：
  - 大风吹模式：继续使用 WindChallengeManager 的距离控制
  - 其他模式：使用新的内置距离控制模式

- 在 `transitionToReadyState()` 方法中：
  - 添加了非大风吹模式的距离控制状态重置

- 在 `restartCurrentBackgroundComponents()` 方法中：
  - 更新了重启逻辑，支持距离控制模式

## 工作原理

### 距离控制生成逻辑
1. **管道生成**：第一个管道在移动距离300处生成，之后每600距离生成一个
2. **金币生成**：第一个金币在移动距离600处生成（即第一个管道后300距离），之后每600距离生成一个
3. **同步保证**：管道和金币都基于相同的移动距离计算，确保金币始终生成在相邻管道的中间

### 复活后的处理
1. 复活时调用 `resetDistanceTracking()` 重置距离追踪
2. 清理场景中的现有管道和金币
3. 重新开始距离控制生成，确保金币位置正确

## 适用范围
- 普通模式：轻松、标准、困难
- 挑战模式：大雾起、大雪飘
- 大风吹模式：继续使用原有的 WindChallengeManager

## 测试要点
1. 验证复活后金币是否生成在管道中间
2. 验证不同难度下的生成间距是否正确
3. 验证挑战模式下的生成是否正常
4. 验证大风吹模式是否不受影响

## 测试步骤

### 1. 基本功能测试
1. 在Cocos Creator中打开项目
2. 运行游戏场景
3. 选择普通模式（轻松、标准或困难）
4. 开始游戏，观察金币是否生成在管道中间
5. 故意让小鸟撞击管道死亡
6. 在游戏结束界面点击复活按钮（确保有复活币）
7. 复活后观察金币生成位置是否仍在管道中间

### 2. 挑战模式测试
1. 选择挑战模式中的大雾起或大雪飘
2. 重复上述测试步骤
3. 验证复活后金币生成位置

### 3. 大风吹模式验证
1. 选择挑战模式中的大风吹
2. 验证功能是否正常（应该不受影响）

## 预期结果
- 所有非大风吹模式下，复活后金币都应该生成在相邻管道的中间线上
- 金币和管道的生成应该保持同步
- 大风吹模式的功能不受影响

## 调试信息
修改后的代码会在控制台输出以下调试信息：
- "金币生成器：启动距离控制模式"
- "管道生成器：启动距离控制模式"
- "距离控制生成金币 - 距离: X, 下次金币距离: Y"
- "距离控制生成管道 - 距离: X, 下次管道距离: Y"

## 注意事项
1. 确保在Cocos Creator编辑器中重新编译项目
2. 如果遇到问题，检查控制台输出的调试信息
3. 确保复活币道具功能正常工作
